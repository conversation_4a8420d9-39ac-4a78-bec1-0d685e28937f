// pages/equipment/detail/detail.js
const app = getApp();
const QRCode = require('../../../utils/weapp-qrcode.js');

Page({
  data: {
    equipmentId: null,
    equipment: null,
    loading: true,
    currentRole: 'user',
    canManage: false,
    canCreateOrder: true,
    orderHistory: [],
    showOrderHistory: false,
    qrCodeData: null,
    qrSize: 200,
    qrCodeInstance: null
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => wx.navigateBack(), 1500);
      return;
    }

    this.setData({ equipmentId: id });
    this.checkAuth();
    this.updateRoleInfo();
    this.loadEquipmentDetail();
  },

  onShow() {
    this.updateRoleInfo();
  },

  onPullDownRefresh() {
    this.loadEquipmentDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    const canManage = ['pi', 'admin', 'super_admin'].includes(currentRole);

    this.setData({
      currentRole,
      canManage
    });
  },

  // 加载设备详情
  async loadEquipmentDetail() {
    try {
      this.setData({ loading: true });

      const result = await app.request({
        url: `/equipment/${this.data.equipmentId}`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          equipment: result.data
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('加载设备详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载维修历史
  async loadOrderHistory() {
    try {
      const result = await app.request({
        url: `/equipment/${this.data.equipmentId}/orders`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          orderHistory: result.data.list || [],
          showOrderHistory: true
        });
      }
    } catch (error) {
      console.error('加载维修历史失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 创建维修工单
  onCreateOrder() {
    if (!this.data.canCreateOrder) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/orders/create/create?equipmentId=${this.data.equipmentId}`
    });
  },

  // 编辑设备
  onEditEquipment() {
    if (!this.data.canManage) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/equipment/edit/edit?id=${this.data.equipmentId}`
    });
  },

  // 查看维修历史
  onViewHistory() {
    this.loadOrderHistory();
  },

  // 隐藏维修历史
  onHideHistory() {
    this.setData({ showOrderHistory: false });
  },

  // 查看工单详情
  onViewOrder(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/orders/detail/detail?id=${id}`
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'normal': '正常',
      'maintenance': '维修中',
      'fault': '故障',
      'retired': '已报废'
    };
    return statusMap[status] || '未知';
  },

  // 获取状态样式
  getStatusClass(status) {
    const classMap = {
      'normal': 'status-normal',
      'maintenance': 'status-maintenance',
      'fault': 'status-fault',
      'retired': 'status-retired'
    };
    return classMap[status] || '';
  },

  // 复制信息
  onCopyInfo(e) {
    const { text } = e.currentTarget.dataset;
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        });
      }
    });
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: [url]
    });
  },

  // 生成二维码
  onGenerateQR() {
    if (!this.data.equipment) {
      wx.showToast({
        title: '设备信息未加载',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '生成中...',
      mask: true
    });

    try {
      // 生成小程序跳转链接
      const miniProgramUrl = this.generateMiniProgramUrl();

      // 保存二维码数据用于显示信息
      const qrData = {
        type: 'equipment',
        version: '1.0',
        timestamp: Date.now(),
        url: miniProgramUrl,
        data: {
          id: this.data.equipment.id,
          name: this.data.equipment.name,
          model: this.data.equipment.model,
          serialNumber: this.data.equipment.serialNumber,
          location: this.data.equipment.location,
          status: this.data.equipment.status,
          qrCode: `EQ-${this.data.equipment.id}-${Date.now()}`
        }
      };

      this.setData({ qrCodeData: qrData });

      // 使用专业的二维码库生成小程序跳转链接
      this.generateQRWithLibrary(miniProgramUrl);

    } catch (error) {
      console.error('生成二维码失败:', error);
      wx.showToast({
        title: '生成失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 生成小程序跳转URL
  generateMiniProgramUrl() {
    const equipment = this.data.equipment;

    // 构建设备信息参数
    const equipmentParams = {
      id: equipment.id,
      name: equipment.name,
      model: equipment.model,
      serialNumber: equipment.serialNumber,
      location: equipment.location,
      status: equipment.status,
      source: 'qrcode' // 标识来源为二维码扫描
    };

    // 将参数编码为URL参数字符串
    const paramString = Object.keys(equipmentParams)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(equipmentParams[key] || '')}`)
      .join('&');

    // 生成小程序页面路径（这里使用一个假设的域名，实际需要在微信后台配置）
    // 格式：https://your-domain.com/miniprogram?page=pages/orders/create/create&参数
    const baseUrl = 'https://ziguanwei.example.com/miniprogram';
    const miniProgramUrl = `${baseUrl}?page=pages/orders/create/create&${paramString}`;

    return miniProgramUrl;
  },

  // 使用weapp-qrcode库生成二维码
  generateQRWithLibrary(data) {
    try {
      // 创建二维码实例
      const qrcode = new QRCode('qrCanvas', {
        usingIn: this,
        text: data,
        width: this.data.qrSize,
        height: this.data.qrSize,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      });

      // 保存实例以便后续使用
      this.setData({ qrCodeInstance: qrcode });

      console.log('二维码生成成功');
    } catch (error) {
      console.error('二维码生成失败:', error);
      wx.showToast({
        title: '二维码生成失败',
        icon: 'error'
      });
    }
  },

  // 保存二维码到相册
  onSaveQRCode() {
    if (!this.data.qrCodeData || !this.data.qrCodeInstance) {
      wx.showToast({
        title: '请先生成二维码',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 使用weapp-qrcode库的导出功能
    this.data.qrCodeInstance.exportImage((tempFilePath) => {
      if (tempFilePath) {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: (error) => {
            wx.hideLoading();
            if (error.errMsg.includes('auth deny')) {
              wx.showModal({
                title: '需要相册权限',
                content: '保存二维码需要访问您的相册，请在设置中开启权限',
                confirmText: '去设置',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting();
                  }
                }
              });
            } else {
              wx.showToast({
                title: '保存失败',
                icon: 'error'
              });
            }
          }
        });
      } else {
        wx.hideLoading();
        wx.showToast({
          title: '生成图片失败',
          icon: 'error'
        });
      }
    });
  },

  // 分享二维码
  onShareQRCode() {
    if (!this.data.qrCodeData || !this.data.qrCodeInstance) {
      wx.showToast({
        title: '请先生成二维码',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '准备分享...',
      mask: true
    });

    // 使用weapp-qrcode库的导出功能
    this.data.qrCodeInstance.exportImage((tempFilePath) => {
      wx.hideLoading();

      if (tempFilePath) {
        wx.showShareImageMenu({
          path: tempFilePath,
          success: () => {
            console.log('分享成功');
          },
          fail: () => {
            wx.showToast({
              title: '分享失败',
              icon: 'error'
            });
          }
        });
      } else {
        wx.showToast({
          title: '生成图片失败',
          icon: 'error'
        });
      }
    });
  }
});