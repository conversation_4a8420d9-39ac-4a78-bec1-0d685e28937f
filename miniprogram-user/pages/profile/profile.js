// pages/profile/profile.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    currentRole: 'user',
    roles: [],
    isMultiRole: false,
    stats: {
      myOrders: 0,
      myEquipment: 0,
      unreadMessages: 0
    }
  },

  onLoad() {
    this.checkAuth();
    this.loadUserInfo();
    this.loadUserStats();
  },

  onShow() {
    this.updateUserInfo();
    this.loadUserStats();
  },

  onPullDownRefresh() {
    Promise.all([
      this.loadUserInfo(),
      this.loadUserStats()
    ]).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新用户信息
  updateUserInfo() {
    this.setData({
      userInfo: app.globalData.userInfo,
      currentRole: app.globalData.currentRole,
      roles: app.globalData.roles,
      isMultiRole: app.globalData.isMultiRole
    });
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const result = await app.request({
        url: '/users/profile',
        method: 'GET'
      });

      if (result.success) {
        const userInfo = result.data;

        app.globalData.userInfo = { ...app.globalData.userInfo, ...userInfo };
        wx.setStorageSync('userInfo', app.globalData.userInfo);

        this.setData({ userInfo });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 加载用户统计
  async loadUserStats() {
    try {
      const result = await app.request({
        url: '/users/stats',
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          stats: result.data
        });
      }
    } catch (error) {
      console.error('加载用户统计失败:', error);
    }
  },

  // 统计数据点击
  onStatTap(e) {
    const { type } = e.currentTarget.dataset;

    switch (type) {
      case 'orders':
        wx.switchTab({
          url: '/pages/orders/orders'
        });
        break;
      case 'equipment':
        wx.switchTab({
          url: '/pages/equipment/equipment'
        });
        break;
      case 'messages':
        wx.switchTab({
          url: '/pages/chat/chat'
        });
        break;
    }
  },

  // 菜单点击
  onMenuTap(e) {
    const { action } = e.currentTarget.dataset;

    switch (action) {
      case 'verify':
        this.onVerify();
        break;
      case 'settings':
        this.onSettings();
        break;
      case 'help':
        this.onHelp();
        break;
      case 'about':
        this.onAbout();
        break;
    }
  },

  // 获取角色文本
  getRoleText(role) {
    const roleMap = {
      'user': '普通用户',
      'pi': 'PI用户',
      'engineer': '工程师',
      'admin': '管理员',
      'super_admin': '超级管理员'
    };
    return roleMap[role] || role;
  },

  // 角色切换回调
  onRoleChange(e) {
    const { newRole } = e.detail;
    this.setData({
      currentRole: newRole
    });
    this.loadUserStats();
  },



  // 编辑个人信息
  onEditProfile() {
    wx.navigateTo({
      url: '/pages/profile/edit/edit'
    });
  },



  // 实名认证
  onVerify() {
    wx.navigateTo({
      url: '/pages/profile/verify/verify'
    });
  },

  // 设置
  onSettings() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 帮助与反馈
  onHelp() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 关于我们
  onAbout() {
    wx.showModal({
      title: '关于资管维',
      content: '版本：1.0.0\n\n资管维是一个专业的设备管理和维修系统，致力于为高校和科研院所提供高效的设备管理解决方案。',
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performLogout();
        }
      }
    });
  },

  // 执行退出登录
  performLogout() {
    // 清除本地存储
    wx.removeStorageSync('token');
    wx.removeStorageSync('refreshToken');
    wx.removeStorageSync('userInfo');

    // 清除全局数据
    app.globalData.token = null;
    app.globalData.refreshToken = null;
    app.globalData.userInfo = null;
    app.globalData.currentRole = 'user';
    app.globalData.roles = [];
    app.globalData.isMultiRole = false;

    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  }
});