<!--pages/chat/detail/detail.wxml-->
<view class="chat-detail-container">
  <!-- 聊天头部 -->
  <view class="chat-header">
    <view class="order-info">
      <text class="order-title">{{orderInfo.title}}</text>
      <text class="order-number">{{orderInfo.order_number}}</text>
    </view>
    <view class="order-status status-{{orderInfo.status}}">
      {{getStatusText(orderInfo.status)}}
    </view>
  </view>

  <!-- 消息列表 -->
  <scroll-view 
    class="message-list" 
    scroll-y="true" 
    scroll-top="{{scrollTop}}"
    scroll-into-view="{{scrollIntoView}}"
  >
    <view 
      class="message-item {{item.sender_id === currentUserId ? 'message-right' : 'message-left'}}"
      wx:for="{{messageList}}"
      wx:key="id"
      id="msg-{{item.id}}"
    >
      <view class="message-avatar" wx:if="{{item.sender_id !== currentUserId}}">
        <image src="{{item.sender.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
      
      <view class="message-content">
        <view class="message-info" wx:if="{{item.sender_id !== currentUserId}}">
          <text class="sender-name">{{item.sender.real_name || item.sender.username}}</text>
          <text class="message-time">{{formatTime(item.created_at)}}</text>
        </view>
        
        <view class="message-bubble {{item.sender_id === currentUserId ? 'bubble-right' : 'bubble-left'}}">
          <text wx:if="{{item.message_type === 'text'}}">{{item.content}}</text>
          <image wx:elif="{{item.message_type === 'image'}}" src="{{item.content}}" mode="widthFix" bindtap="onPreviewImage" data-url="{{item.content}}"></image>
          <view wx:elif="{{item.message_type === 'file'}}" class="file-message" bindtap="onDownloadFile" data-url="{{item.content}}">
            <text class="file-icon">📄</text>
            <text class="file-name">{{item.file_name || '文件'}}</text>
          </view>
        </view>
        
        <view class="message-info" wx:if="{{item.sender_id === currentUserId}}">
          <text class="message-time">{{formatTime(item.created_at)}}</text>
        </view>
      </view>
      
      <view class="message-avatar" wx:if="{{item.sender_id === currentUserId}}">
        <image src="{{currentUser.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-tools">
      <view class="tool-btn" bindtap="onChooseImage">
        <text class="tool-icon">📷</text>
      </view>
      <view class="tool-btn" bindtap="onChooseFile">
        <text class="tool-icon">📎</text>
      </view>
    </view>
    
    <view class="input-wrapper">
      <textarea 
        class="message-input"
        placeholder="输入消息..."
        value="{{inputText}}"
        bindinput="onInputChange"
        auto-height
        maxlength="500"
        show-confirm-bar="{{false}}"
      ></textarea>
      <button 
        class="send-btn {{inputText.trim() ? 'send-active' : ''}}" 
        bindtap="onSendMessage"
        disabled="{{!inputText.trim() || sending}}"
      >
        {{sending ? '发送中' : '发送'}}
      </button>
    </view>
  </view>
</view>
