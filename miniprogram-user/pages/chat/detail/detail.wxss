/* pages/chat/detail/detail.wxss */
.chat-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.order-number {
  display: block;
  font-size: 22rpx;
  color: #666666;
}

.order-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-assigned {
  background: #cce5ff;
  color: #0066cc;
}

.status-accepted {
  background: #d4edda;
  color: #155724;
}

.status-in_progress {
  background: #e2e3f1;
  color: #6c757d;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
}

.message-item.message-right {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  margin: 0 16rpx;
  flex-shrink: 0;
}

.message-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-right .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-info {
  margin-bottom: 8rpx;
}

.message-right .message-info {
  text-align: right;
}

.sender-name {
  font-size: 22rpx;
  color: #666666;
  margin-right: 16rpx;
}

.message-right .sender-name {
  margin-right: 0;
  margin-left: 16rpx;
}

.message-time {
  font-size: 20rpx;
  color: #999999;
}

.message-bubble {
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  line-height: 1.4;
  word-wrap: break-word;
  max-width: 100%;
}

.bubble-left {
  background: #ffffff;
  color: #333333;
  border-top-left-radius: 4rpx;
}

.bubble-right {
  background: #1976D2;
  color: #ffffff;
  border-top-right-radius: 4rpx;
}

.message-bubble image {
  max-width: 100%;
  border-radius: 8rpx;
}

.file-message {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.bubble-left .file-message {
  background: #f5f5f5;
  border-color: #e0e0e0;
}

.file-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.file-name {
  font-size: 24rpx;
  flex: 1;
}

/* 输入区域 */
.input-section {
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx;
}

.input-tools {
  display: flex;
  margin-bottom: 16rpx;
}

.tool-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

.tool-btn:active {
  background: #e0e0e0;
}

.tool-icon {
  font-size: 28rpx;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  min-height: 60rpx;
  max-height: 120rpx;
  padding: 16rpx 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.4;
  margin-right: 16rpx;
}

.send-btn {
  width: 120rpx;
  height: 60rpx;
  background: #cccccc;
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.send-btn.send-active {
  background: #1976D2;
}

.send-btn:disabled {
  background: #cccccc;
  color: #999999;
}

.send-btn.send-active:active {
  background: #1565C0;
}
